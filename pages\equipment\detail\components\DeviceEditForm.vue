<template>
  <view class="popup-content">
    <view class="point-name">
      <view class="pop-item flex align-center">
        <view class="pop-label"> 名称： </view>
        <view class="pop-content">
          <u-input
            v-model="localEditForm.name"
            maxlength="12"
            placeholder="请输入点位名称"
            border="none"
            @input="handleInput">
          </u-input>
        </view>
      </view>
      <view class="pop-item flex align-center">
        <view class="pop-label"> 点位： </view>
        <view
          class="pop-content"
          @click="$emit('navigateToPoint')">
          <u-input
            v-model="localEditForm.placeName"
            placeholder="请选择点位"
            disabled
            disabledColor="#fff"
            border="none"
            suffixIcon="arrow-right"
            :suffixIconStyle="{ fontSize: 24, color: '#555555' }">
          </u-input>
        </view>
      </view>
    </view>

    <view class="point-msg">
      <view class="pop-item flex align-center">
        <view class="pop-label"> 点位名称： </view>
        <view class="pop-content">
          {{ pointDetail.placeName || "-" }}
        </view>
      </view>
      <view class="pop-item flex align-center">
        <view class="pop-label"> 场景： </view>
        <view class="pop-content">
          {{ pointDetail.sceneNames || "-" }}
        </view>
      </view>
      <view class="pop-item flex align-center">
        <view class="pop-label"> 管理员： </view>
        <view class="pop-content">
          {{ pointDetail.adminName || "-" }}
        </view>
      </view>
      <view class="pop-item flex align-center">
        <view class="pop-label"> 区域/线路： </view>
        <view class="pop-content">
          {{ pointDetail.regionName || "-" }}
        </view>
      </view>
      <view class="pop-item flex align-center">
        <view class="pop-label"> 地址： </view>
        <view class="pop-content">
          {{ pointDetail.address || "-" }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DeviceEditForm',
  props: {
    editForm: {
      type: Object,
      default: () => ({
        placeId: null,
        name: "",
        placeName: null,
      })
    },
    pointDetail: {
      type: Object,
      default: () => ({
        placeName: "",
        sceneNames: "",
        adminName: "",
        regionName: "",
        address: "",
      })
    }
  },
  data() {
    return {
      localEditForm: { ...this.editForm }
    }
  },
  watch: {
    editForm: {
      handler(newVal) {
        this.localEditForm = { ...newVal }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleInput() {
      this.$emit('update:editForm', this.localEditForm)
    }
  }
}
</script>

<style scoped>
.popup-content {
  padding: 20rpx;
}

.point-name {
  margin-bottom: 30rpx;
}

.pop-item {
  margin-bottom: 20rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f1f1f1;
}

.pop-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #333;
}

.pop-content {
  flex: 1;
  font-size: 28rpx;
  color: #666;
}

.point-msg .pop-content {
  color: #999;
}
</style>
