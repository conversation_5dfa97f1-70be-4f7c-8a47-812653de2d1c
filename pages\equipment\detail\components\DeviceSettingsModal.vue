<template>
  <xpopup
    :show="show"
    @close="$emit('close')"
    @confirm="handleConfirm"
    :showBtn="true"
    :title="title">
    
    <!-- 设备编辑 -->
    <device-edit-form
      v-if="title == '编辑'"
      :editForm="editForm"
      :pointDetail="pointDetail"
      @update:editForm="$emit('update:editForm', $event)"
      @navigateToPoint="$emit('navigateToPoint')"
    />

    <!-- 确认对话框 -->
    <confirm-dialog
      v-if="isConfirmDialog"
      :message="confirmMessage"
    />

    <!-- 声音设置 -->
    <voice-settings
      v-if="title == '声音设置'"
      :voice="voice"
      @update:voice="$emit('update:voice', $event)"
    />

    <!-- 告警设置 -->
    <alarm-settings
      v-if="title == '告警设置'"
      :tempNot="tempNot"
      :tempNotRule="tempNotRule"
      :detail="detail"
      @update:tempNot="$emit('update:tempNot', $event)"
      ref="alarmSettings"
    />

    <!-- 温度设置 -->
    <temperature-settings
      v-if="title == '温度设置'"
      :tempDetail="tempDetail"
      :tempType="tempType"
      :tempWorkModels="tempWorkModels"
      :tempTypeModels="tempTypeModels"
      @update:tempDetail="$emit('update:tempDetail', $event)"
      @update:tempType="$emit('update:tempType', $event)"
      @pickerTimes="$emit('pickerTimes', $event)"
    />

    <!-- 灯光设置 -->
    <light-settings
      v-if="title == '灯光设置'"
      :lightDetail="lightDetail"
      @update:lightDetail="$emit('update:lightDetail', $event)"
      @pickerTimes="$emit('pickerTimes', $event)"
    />

    <!-- 灯箱设置 -->
    <ad-light-settings
      v-if="title == '灯箱设置'"
      :adlightDetail="adlightDetail"
      @update:adlightDetail="$emit('update:adlightDetail', $event)"
      @pickerTimes="$emit('pickerTimes', $event)"
    />

    <!-- 蓝牙授权 -->
    <bluetooth-auth
      v-if="title == '蓝牙授权'"
      :detail="detail"
      :blueCode="blueCode"
      @update:blueCode="$emit('update:blueCode', $event)"
    />

    <!-- 换绑SN -->
    <sn-binding
      v-if="title == '换绑SN'"
      :detail="detail"
      :snCode="snCode"
      @update:snCode="$emit('update:snCode', $event)"
    />

    <!-- 加热丝设置 -->
    <heater-settings
      v-if="title == '加热丝设置'"
      :jrsDetail="jrsDetail"
      @update:jrsDetail="$emit('update:jrsDetail', $event)"
      @pickerTimes="$emit('pickerTimes', $event)"
      @jrsClick="$emit('jrsClick')"
    />
  </xpopup>
</template>

<script>
import DeviceEditForm from './DeviceEditForm.vue'
import ConfirmDialog from './ConfirmDialog.vue'
import VoiceSettings from './VoiceSettings.vue'
import AlarmSettings from './AlarmSettings.vue'
import TemperatureSettings from './TemperatureSettings.vue'
import LightSettings from './LightSettings.vue'
import AdLightSettings from './AdLightSettings.vue'
import BluetoothAuth from './BluetoothAuth.vue'
import SnBinding from './SnBinding.vue'
import HeaterSettings from './HeaterSettings.vue'

export default {
  name: 'DeviceSettingsModal',
  components: {
    DeviceEditForm,
    ConfirmDialog,
    VoiceSettings,
    AlarmSettings,
    TemperatureSettings,
    LightSettings,
    AdLightSettings,
    BluetoothAuth,
    SnBinding,
    HeaterSettings
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    detail: {
      type: Object,
      default: () => ({})
    },
    btnState: {
      type: String,
      default: ''
    },
    editForm: {
      type: Object,
      default: () => ({})
    },
    pointDetail: {
      type: Object,
      default: () => ({})
    },
    voice: {
      type: Number,
      default: 4
    },
    tempNot: {
      type: Object,
      default: () => ({})
    },
    tempNotRule: {
      type: Object,
      default: () => ({})
    },
    tempDetail: {
      type: Object,
      default: () => ({})
    },
    tempType: {
      type: [String, Number],
      default: 0
    },
    tempWorkModels: {
      type: Array,
      default: () => []
    },
    tempTypeModels: {
      type: Array,
      default: () => []
    },
    lightDetail: {
      type: Object,
      default: () => ({})
    },
    adlightDetail: {
      type: Object,
      default: () => ({})
    },
    blueCode: {
      type: String,
      default: ''
    },
    snCode: {
      type: String,
      default: ''
    },
    jrsDetail: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    isConfirmDialog() {
      const confirmDialogs = ['设备重启', '开侧门', '暂停营业', '开始营业', '清除故障']
      return confirmDialogs.includes(this.title)
    },
    confirmMessage() {
      const messages = {
        '设备重启': '是否确定重启设备?',
        '开侧门': '是否确定开侧门?',
        '暂停营业': `是否确定${this.btnState}?`,
        '开始营业': `是否确定${this.btnState}?`,
        '清除故障': '是否确定清除故障?'
      }
      return messages[this.title] || ''
    }
  },
  methods: {
    handleConfirm() {
      // 如果是告警设置，需要验证表单
      if (this.title === '告警设置') {
        this.$refs.alarmSettings.validate().then(() => {
          this.$emit('confirm')
        }).catch(() => {
          // 验证失败，不触发确认事件
        })
      } else {
        this.$emit('confirm')
      }
    }
  }
}
</script>
